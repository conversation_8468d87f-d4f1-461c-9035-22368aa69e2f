# Tembo Integration Analysis & Solution

## 🔍 **Problem Diagnosis**

### **Root Causes of Pending Collections with N/A Reasons**

1. **Missing Polling Logic**: TZS_COLLECTIONS transactions were not being actively polled for status updates
2. **Webhook-Only Dependency**: System relied entirely on Tembo webhooks which can fail
3. **Incomplete Error Mapping**: Limited status code handling and poor error reason capture
4. **No Proactive Monitoring**: No diagnostic tools to identify and resolve stuck transactions
5. **Database Schema Mismatch**: <PERSON> was trying to update non-existent `reason` field instead of `message` field

## 🚨 **CRITICAL DATABASE SCHEMA FIXES**

### **Field Name Corrections**
- **transactions.reason** ❌ → **transactions.message** ✅ (VARCHAR 255)
- **webhook_logs.timestamp** ❌ → **webhook_logs.created_at** ✅ (TIMESTAMP)

### **Verified Database Schema**
```sql
-- transactions table (relevant fields)
CREATE TABLE transactions (
    trans_id VARCHAR(255) UNIQUE NOT NULL,
    status ENUM('PENDING', 'FAILED', 'SUCCESS', 'INITIATED', 'RECEIVED', 'ONHOLD', ...),
    message VARCHAR(255) NULL,  -- ✅ This is the correct field for storing reasons
    ext_reference VARCHAR(60) NULL,
    service_name VARCHAR(40) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- transactions_log table
CREATE TABLE transactions_log (
    trans_id VARCHAR(255) NOT NULL,
    status VARCHAR(100) NULL,
    step VARCHAR(40) NULL,
    response_code VARCHAR(10) NULL,
    description TEXT NULL,  -- ✅ Used for detailed logging
    data TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- webhook_logs table
CREATE TABLE webhook_logs (
    trans_id VARCHAR(50) NULL,
    webhook_data TEXT NULL,
    event VARCHAR(40) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP  -- ✅ Correct timestamp field
);
```

## 🛠️ **Implemented Solutions**

### **1. Added Tembo Status Polling** ✅
- **File**: `wallet/src/services/polling.collections.service.ts`
- **New Method**: `checkTemboCollectionStatus()`
- **Integration**: Added TZS_COLLECTIONS case in `handlePendingCollectionTransaction()`

**Key Features**:
- Proactive status checking via Tembo API
- Comprehensive status mapping (PAYMENT_ACCEPTED, PAYMENT_REJECTED, PAYMENT_FAILED, etc.)
- Detailed error logging with specific reasons
- Fallback for unknown status codes

### **2. Enhanced Webhook Processing** ✅
- **File**: `wallet/src/models/thirdParty.ts`
- **Improvements**: Exact status mapping matching Tembo documentation

**Webhook Payload Format** (from Tembo docs):
```json
{
  "statusCode": "PAYMENT_ACCEPTED",
  "transactionRef": "20f807fe-3ee8-4525-8aff-ccb95de38250",
  "transactionId": "X50jcLD-U"
}
```

**Status Mappings** (EXACT MATCH TO TEMBO DOCS):
```typescript
// Collection Status Codes (from Tembo Documentation)
PENDING_ACK → PENDING ("Payment request acknowledged, waiting for customer action")
PAYMENT_ACCEPTED → RECEIVED ("Payment accepted by customer")
PAYMENT_REJECTED → FAILED ("Payment rejected by customer")
PAYMENT_FAILED → FAILED ("Payment failed - network or technical issue")
GENERIC_ERROR → FAILED ("Generic error occurred during payment processing")

// Payout Status Codes (from Tembo Documentation)
PENDING_ACK → PENDING ("Payout request acknowledged")
PAYMENT_ACCEPTED → SUCCESS ("Payout successful")
PAYMENT_REJECTED → FAILED ("Payout rejected")
PAYMENT_FAILED → FAILED ("Payout failed")
```

### **3. Diagnostic Service** ✅
- **File**: `wallet/src/services/tembo.diagnostics.service.ts`
- **Purpose**: Monitor, analyze, and troubleshoot Tembo collections

**Capabilities**:
- Comprehensive transaction analysis
- Success rate calculation
- Common failure reason identification
- Force status checking for stuck transactions
- Bulk processing for multiple transactions

### **4. API Endpoints for Monitoring** ✅
- **File**: `wallet/src/controllers/internal.ts`

**New Endpoints**:
```
GET  /internal/tembo/diagnostics?hours=24
POST /internal/tembo/force-check/:transactionId
POST /internal/tembo/bulk-force-check
```

## 🔗 **Tembo API Endpoints Used**

Based on the official Tembo documentation:

### **Collections (Pulls)**
- **Endpoint**: `POST https://sandbox.temboplus.com/tembo/v1/collection`
- **Status Check**: `POST https://sandbox.temboplus.com/tembo/v1/collection/status`
- **Channels**: `TZ-TIGO-C2B`, `TZ-AIRTEL-C2B`

### **Payouts (Pushes)**
- **Endpoint**: `POST https://sandbox.temboplus.com/tembo/v1/payment/wallet-to-mobile`
- **Status Check**: `POST https://sandbox.temboplus.com/tembo/v1/payment/status`
- **Service Codes**: `TZ-TIGO-B2C`, `TZ-AIRTEL-B2C`

### **Headers Required**
```
x-account-id: [Partner account id]
x-secret-key: [Partner secret key]
x-request-id: [Unique request identifier]
Content-Type: application/json
```

## 📊 **How to Achieve 70% Success Rate**

### **Immediate Actions**

1. **Run Diagnostics**:
```bash
curl "http://your-api/v1/wallet/internal/tembo/diagnostics?hours=24"
```

2. **Force Check Pending Transactions**:
```bash
curl -X POST "http://your-api/v1/wallet/internal/tembo/bulk-force-check" \
  -H "Content-Type: application/json" \
  -d '{"maxTransactions": 100}'
```

### **Monitoring Setup**

1. **Daily Diagnostic Reports**:
   - Schedule daily runs of the diagnostic service
   - Monitor success rate trends
   - Alert when success rate drops below 70%

2. **Real-time Alerts**:
   - Set up alerts for transactions pending > 30 minutes
   - Monitor webhook delivery failures
   - Track Tembo API response times

### **Operational Procedures**

1. **Regular Status Checks**:
   - Polling service now runs every 30 seconds for INITIATED transactions
   - Automatic status updates reduce webhook dependency

2. **Manual Intervention**:
   - Use force check endpoints for stuck transactions
   - Investigate transactions with unknown status codes
   - Monitor common failure patterns

## 🔧 **Testing the Solution**

### **1. Test Polling Logic**
```typescript
// Check if TZS_COLLECTIONS are being polled
const pendingTransactions = await db.query(`
  SELECT * FROM transactions 
  WHERE service_name = 'TZS_COLLECTIONS' 
  AND status = 'INITIATED' 
  AND created_at <= DATE_SUB(NOW(), INTERVAL 30 SECOND)
`);
```

### **2. Test Diagnostic Service**
```bash
# Run diagnostics
curl "http://localhost:3000/v1/wallet/internal/tembo/diagnostics"

# Expected response:
{
  "success": true,
  "data": {
    "totalPending": 25,
    "withReasons": 18,
    "withoutReasons": 7,
    "successRate": 72.0,
    "commonFailureReasons": [...],
    "recommendations": [...]
  }
}
```

### **3. Test Force Status Check**
```bash
# Force check specific transaction
curl -X POST "http://localhost:3000/v1/wallet/internal/tembo/force-check/TXN123456"

# Expected response:
{
  "success": true,
  "data": {
    "success": true,
    "status": "FAILED",
    "reason": "Payment rejected by mobile network operator",
    "temboData": {...}
  }
}
```

## 📈 **Expected Improvements**

### **Before Implementation**:
- 30% success rate in identifying failure reasons
- Many transactions stuck as "pending" with "N/A" reasons
- No proactive monitoring or resolution

### **After Implementation**:
- **Target: 70%+ success rate** in identifying failure reasons
- Automatic status updates via polling
- Detailed failure reasons for better customer support
- Proactive identification and resolution of stuck transactions

## 🚨 **Monitoring & Alerts**

### **Key Metrics to Track**:
1. **Success Rate**: % of transactions with clear status/reason
2. **Pending Duration**: Average time transactions stay pending
3. **Webhook Delivery**: Success rate of webhook processing
4. **API Health**: Tembo API response times and error rates

### **Alert Thresholds**:
- Success rate < 70%
- Transactions pending > 30 minutes
- Webhook delivery failure rate > 10%
- Tembo API error rate > 5%

## 🔄 **Next Steps**

1. **Deploy Changes**: Deploy the updated code to production
2. **Run Initial Diagnostics**: Get baseline metrics
3. **Force Check Backlog**: Process existing pending transactions
4. **Set Up Monitoring**: Implement alerts and daily reports
5. **Monitor & Optimize**: Track success rate and adjust thresholds

## 📋 **Maintenance Tasks**

### **Daily**:
- Review diagnostic reports
- Check for transactions pending > 2 hours
- Monitor success rate trends

### **Weekly**:
- Analyze common failure patterns
- Review Tembo API performance
- Update status mapping if needed

### **Monthly**:
- Review and optimize polling intervals
- Analyze customer impact of failed transactions
- Update diagnostic thresholds based on trends

---

**Expected Outcome**: With these changes, you should achieve a 70%+ success rate in identifying transaction failure reasons, significantly reducing "N/A" statuses and improving customer support capabilities.
