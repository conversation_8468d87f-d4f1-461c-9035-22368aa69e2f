import express, { Request, Response } from "express";
import { JWTMiddleware } from "../helpers/jwt.middleware";
import Internal from "../models/internal";
import ThirdParty from "../models/thirdParty";


const router = express.Router();
const internal = new Internal();
const thirdParty = new ThirdParty();


const applyJWTConditionally = (req: Request, res: Response, next: any) => {
    JWTMiddleware.verifyTokenAccess(req, res, next);
    // next()
};

const saveApiLog = (req: Request, res: Response, next: any) => {
    // thirdPartyHandler.saveApiLog(req.body, req.ip || "");  
    next();
}
router.post("/send-callback",  sendCallBack);
router.post("/webhooks/honeycoin",  sendHonryCoinWebhook);
router.post("/webhooks/tembo",  sendTemboWebhook);
router.post("/initiate-sweep",  initiateSweep);


async function sendHonryCoinWebhook(req: Request, res: Response) {
  try {

    const result = await thirdParty.sendHonryCoinWebhook(req.body);
    res.status(200).json(result);

  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function sendTemboWebhook(req: Request, res: Response) {
  try {
    console.log("🔹 Tembo webhook received:", req.body);
    const result = await thirdParty.sendTemboWebhook(req.body);
    res.status(200).json(result);

  } catch (error: any) {
    console.error("❌ Error processing Tembo webhook:", error);
    res.status(500).json({ message: "Server error", error });
  }
}

async function sendCallBack(req: Request, res: Response) {
  try {
    const result = await internal.sendCallBack(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}

async function initiateSweep(req: Request, res: Response) {
  try {
    const result = await internal.initiateSweep(req.body);
    res.status(200).json(result);
  } catch (error: any) {
    res.status(500).json({ message: "Server error", error });
  }
}


export default router;
