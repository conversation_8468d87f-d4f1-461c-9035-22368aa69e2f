import Model, { Steps } from "../helpers/model";
import { v4 as uuidv4 } from "uuid";
import { symbol, z } from "zod";
import dotenv from "dotenv";
import crypto from "crypto";
import { query } from "express";
import Decimal from 'decimal.js';
import HoneyCoin from "../intergrations/HoneyCoin";
import ThirdPartyHandler from "../helpers/ThirdPartyHandler";
import Transactions from "./transactions";
import { StatusCodes } from "../intergrations/interfaces";
const transactions = new Transactions();

class ThirdParty extends Model {

    private honeycoin: HoneyCoin;
    private thirdPartyHandler: ThirdPartyHandler;

    constructor() {
        super();
        this.honeycoin = new HoneyCoin();
        this.thirdPartyHandler = new ThirdPartyHandler();
    }





    async sendHonryCoinWebhook(data: any) {
        try {
            
            //array of pending collection transactions
            let pendingCollectionTransactions = [];
            // Check if order exists and is available
            const existingOrder: any = await this.callQuerySafe(
                "SELECT * FROM transactions WHERE trans_type = 'PULL' AND status = 'INITIATED' AND currency = 'KES' AND service_name = 'MPESA_COLLECTION'",
                []
            );  

            if (existingOrder.length > 0) {
               pendingCollectionTransactions = existingOrder.map((item: any) => item.trans_id);
            }

            console.log("🔹 Pending collection honey coin webhooks -----------100000--------> :", data)
            if (data.success && data.data && Array.isArray(data.data)) {
                for (const webhookItem of data.data) {
                    if (webhookItem.data) {

                        const { status, type, transactionId, externalReference } = webhookItem.data;
                        //  check if externalReference is in pendingCollectionTransactions
                        if (status === "success" && type === "deposit" && pendingCollectionTransactions.includes(externalReference)) {
                            console.log(`Processing successful deposit for transaction: ${transactionId}`);
                            const result = await transactions.issueTokens(externalReference);
                            console.log(`Token issuance result for ${transactionId}:`, result);
                        } 
                        
                    }
                }
            }
            return this.makeResponse(StatusCodes.SUCCESS.code, "Webhook processed successfully", {});

        } catch (error: any) {
            console.error("Error sending HonryCoin webhook:", error);
            throw new Error("Failed to send HonryCoin webhook");
        }
    }

    /**
     * Process Tembo webhook - simplified to match actual Tembo webhook payload
     * @param data Webhook payload from Tembo: { statusCode, transactionRef, transactionId }
     */
    async sendTemboWebhook(data: any) {
        // Generate unique log ID for webhook tracking
        const cl_id = this.thirdPartyHandler.getTransId();
        const clientId = "TEMBO";
        const transId = data.transactionRef || cl_id;
        const callbackUrl = "";
        const event = "transaction_status";
        const status = 202;

        console.log("🔹 Tembo webhook received:", data);

        // Log incoming webhook
        await this.thirdPartyHandler.saveWebhookLog(
            cl_id,
            clientId,
            transId,
            callbackUrl,
            event,
            data,
            status
        );

        try {
            // Validate required fields from Tembo webhook
            const { statusCode, transactionRef, transactionId } = data;

            if (!statusCode || !transactionRef || !transactionId) {
                console.error("❌ Missing required fields in Tembo webhook", { statusCode, transactionRef, transactionId });
                const errorResponse = this.makeResponse(400, "Missing required fields: statusCode, transactionRef, or transactionId");

                await this.thirdPartyHandler.updateWebhookLog(
                    cl_id,
                    errorResponse,
                    400,
                    this.thirdPartyHandler.formatedDate(new Date())
                );

                return errorResponse;
            }

            console.log(`🔹 Processing Tembo webhook for transaction ${transactionRef} with status ${statusCode}`);

            // Check if this is a pending collection transaction
            const pendingTransaction: any = await this.callQuerySafe(
                "SELECT * FROM transactions WHERE trans_id = ? AND status = 'INITIATED' AND trans_type = 'PULL' AND service_name = 'TZS_COLLECTIONS'",
                [transactionRef]
            );

            if (pendingTransaction.length > 0) {
                console.log(`✅ Found pending TZS collection transaction ${transactionRef} - updating status`);


                let newStatus = 'PENDING';
                let statusReason = 'Transaction status updated via webhook';

                if (statusCode === 'PAYMENT_ACCEPTED') {
                    newStatus = 'RECEIVED';
                    statusReason = 'Payment accepted by customer';
                } else if (statusCode === 'PAYMENT_FAILED') {
                    newStatus = 'FAILED';
                    statusReason = data.reason || data.message || 'Payment failed - customer declined or insufficient funds';
                } else if (statusCode === 'PAYMENT_REJECTED') {
                    newStatus = 'FAILED';
                    statusReason = data.reason || data.message || 'Payment rejected by mobile network operator';
                } else if (statusCode === 'GENERIC_ERROR') {
                    newStatus = 'FAILED';
                    statusReason = data.reason || data.message || 'Generic error occurred during payment processing';
                } else if (statusCode === 'PENDING_ACK') {
                    newStatus = 'PENDING';
                    statusReason = 'Payment request acknowledged, waiting for customer action';
                } else {
                    // Unknown status code - log for investigation
                    newStatus = 'PENDING';
                    statusReason = `Unknown status code received: ${statusCode}`;
                    console.warn(`⚠️ Unknown Tembo status code: ${statusCode}`, data);
                }
                 
                await this.updateData("transactions", `trans_id = '${transactionRef}'`, {
                    status: newStatus,
                    ext_reference: transactionId,
                    reason: statusReason  // ✅ Store the detailed reason
                });

                // Log the transaction update with detailed reason
                await this.insertData("transactions_log", {
                    trans_id: transactionRef,
                    status: newStatus,
                    step: 'TEMBO_WEBHOOK',
                    response_code: 200,
                    description: statusReason,
                    data: JSON.stringify(data)
                });

                // If payment was accepted, issue tokens
                if (statusCode === 'PAYMENT_ACCEPTED') {
                    console.log(`🎯 Payment accepted - issuing tokens for transaction ${transactionRef}`);
                    const result = await transactions.issueTokens(transactionRef);
                    console.log(`🎯 Token issuance result for ${transactionRef}:`, result);
                }

                const successResponse = this.makeResponse(200, "Tembo webhook processed successfully", {
                    transactionRef,
                    temboTransactionId: transactionId,
                    statusCode,
                    newStatus
                });

                await this.thirdPartyHandler.updateWebhookLog(
                    cl_id,
                    successResponse,
                    200,
                    this.thirdPartyHandler.formatedDate(new Date())
                );

                return successResponse;
            } else {
                console.log(`ℹ️ Tembo webhook for transaction ${transactionRef} - not found in pending collections`);

                const infoResponse = this.makeResponse(200, "Webhook received but transaction not found in pending collections", {
                    transactionRef,
                    temboTransactionId: transactionId,
                    statusCode,
                    reason: "Transaction not found in pending collections or already processed"
                });

                await this.thirdPartyHandler.updateWebhookLog(
                    cl_id,
                    infoResponse,
                    200,
                    this.thirdPartyHandler.formatedDate(new Date())
                );

                return infoResponse;
            }

        } catch (error: any) {
            console.error("❌ Error processing Tembo webhook:", error);

            const errorResponse = this.makeResponse(500, "Failed to process Tembo webhook", {
                error: error.message
            });

            await this.thirdPartyHandler.updateWebhookLog(
                cl_id,
                errorResponse,
                500,
                this.thirdPartyHandler.formatedDate(new Date())
            );

            return errorResponse;
        }
    }







    

}

export default ThirdParty;

