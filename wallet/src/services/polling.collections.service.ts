import Transactions from "../models/transactions";
import ThirdPartyHandler from "../helpers/ThirdPartyHandler";
import PegaPay from "../intergrations/PegPay";
import MyFX from "../intergrations/MyFX";
import { Steps } from "../helpers/model";
import HoneyCoin from "../intergrations/HoneyCoin";
import Tembo from "../intergrations/Tembo";
import { StatusCodes } from "../intergrations/interfaces";
import { getItem, setItem } from "../helpers/connectRedis";
import Model from "../helpers/model";


const thirdPartyHandler = new ThirdPartyHandler();
const pg = new PegaPay();
const tembo = new Tembo();

interface PollingResponse {
    statusCode: string;    // "0", "400", "122", "500"
    transStatus: string;   // "SUCCESS", "FAILED", "PENDING", "ONHOLD"
    description: string;   // Human-readable description
    data: any;            // Original response data
}

class PollingService extends Model {

    async getCollectionTransactions() {
        // only grab INITIATED PULLs that were created ≥30 seconds ago
        const transactions: any = await this.callRawQuery(`
          SELECT *
            FROM transactions
           WHERE status     = 'INITIATED'
             AND trans_type = 'PULL'
             AND created_at <= DATE_SUB(NOW(), INTERVAL 30 SECOND)
        `);

        for (let i = 0; i < transactions.length; i++) {
            console.log(`getCollectionTransactions`, transactions[i]);
            const result = await new Transactions().handlePendingCollectionTransaction(transactions[i]);
        }
        return true
    }



    // interac status check
    async checkInterac(ext_reference: any, amount: any): Promise<PollingResponse> {
        console.log(`INTERAC_STATUS_CHECK`, ext_reference);
        const interacResp = await MyFX.confirmPayment(ext_reference)
        console.log(`INTERAC_STATUS_CHECK`, interacResp);

        if (interacResp.success == true) {
            const paidAmount = interacResp.data.amount
            const updateDb = await this.updateData("transactions", `ext_reference='${ext_reference}'`, { status: "RECEIVED", amount: paidAmount });
            this.saveTransactionLog(ext_reference, "SUCCESS", Steps.THIRDPARTY_RESPONSE, 200, "RECEIVED", interacResp)
            return this.mapCollectionResponse(0, "SUCCESS", "SUCCESS", interacResp)
        } else if (interacResp.transaction_status == "pending") {
            this.saveTransactionLog(ext_reference, "PENDING", Steps.THIRDPARTY_RESPONSE, 202, "PENDING", interacResp.data)
            return this.mapCollectionResponse(122, "PENDING", "PENDING", interacResp)
        } else {
            this.saveTransactionLog(ext_reference, "FAILED", Steps.THIRDPARTY_RESPONSE, 400, "FAILED", interacResp.data)
            return this.mapCollectionResponse(400, "FAILED", "FAILED", interacResp.data)
        }
    }

    // pagasus status check
    async CheckMoMoStatus(trans_id: any): Promise<PollingResponse> {
        const pgStatus: any = await pg.getTransactionDetails(trans_id, "PULL");
        return this.mapCollectionResponse(pgStatus.statusCode, pgStatus.description, pgStatus.description)
    }

    //get honey coin transaction details
    async getHoneyCoinTransaction(transactionId: string): Promise<PollingResponse> {
        try {

            let statusCode = 0
            const honeycoin = new HoneyCoin();
            const transaction = await honeycoin.getTransaction(transactionId);
            console.log("🔹 HoneyCoin transaction:", transaction)
            if (transaction?.data?.status === "SUCCESSFUL") {

                statusCode = 0
                this.saveTransactionLog(transactionId, "SUCCESS", Steps.THIRDPARTY_RESPONSE, 200, "SUCCESS", transaction.data)
                return this.mapCollectionResponse(statusCode, "SUCCESS", "SUCCESS", transaction.data)
            } else if (transaction?.data?.status === "FAILED") {

                statusCode = 400
                this.saveTransactionLog(transactionId, "FAILED", Steps.THIRDPARTY_RESPONSE, 400, "FAILED", transaction.data)
                return this.mapCollectionResponse(statusCode, transaction?.data?.note || "FAILED", "FAILED", transaction.data)
            } else {

                statusCode = 122
                return this.mapCollectionResponse(statusCode, "PENDING", "PENDING",  transaction.data)
            }
        } catch (error: any) {

            console.error("Error getting HoneyCoin transaction:", error);
            const errorMessage: any = error?.message || "Transaction failed"
            this.saveTransactionLog(transactionId, "ERROR", Steps.THIRDPARTY_RESPONSE, 500, "FAILED",  errorMessage)
            return this.mapCollectionResponse(500, "ONHOLD", "ONHOLD", errorMessage);
        }
    }

    /**
     * Check Tembo collection transaction status
     * This provides the missing polling logic for TZS_COLLECTIONS
     * Now with robust Tembo transaction ID validation
     */
    async checkTemboCollectionStatus(transactionId: string): Promise<PollingResponse> {
        try {
            console.log("🔹 Checking Tembo collection status for:", transactionId);

            // Get transaction details including creation time and ext_reference
            const transactionQuery = `
                SELECT ext_reference, created_at, status
                FROM transactions
                WHERE trans_id = ? AND service_name = 'TZS_COLLECTIONS'
            `;
            const transactionResult = await this.callQuerySafe(transactionQuery, [transactionId]) as any[];

            if (!transactionResult || transactionResult.length === 0) {
                console.log("❌ Transaction not found in database:", transactionId);
                return { statusCode: "404", description: "Transaction not found", transStatus: "FAILED", data: null };
            }

            const transaction = transactionResult[0];
            const temboTransactionId = transaction.ext_reference;
            const createdAt = new Date(transaction.created_at);
            const now = new Date();
            const ageInMinutes = (now.getTime() - createdAt.getTime()) / (1000 * 60);

            console.log("🔹 Transaction analysis:", {
                ourTransactionId: transactionId,
                temboTransactionId: temboTransactionId,
                ageInMinutes: Math.round(ageInMinutes),
                currentStatus: transaction.status
            });

            // Strategy: Ensure we have Tembo transaction ID before status check
            if (!temboTransactionId) {
                return await this.handleMissingTemboTransactionId(transactionId, ageInMinutes);
            }

            // We have Tembo transaction ID - proceed with status check
            console.log("✅ Tembo transaction ID available - proceeding with status check");
            const response = await tembo.getCollectionStatus(transactionId, temboTransactionId);
            console.log("🔹 Tembo status response:", response);

            let statusCode = 122; // Default to pending
            let description = "PENDING";
            let transStatus = "PENDING";

            if (response.status === 200 && response.data) {
                const temboStatus = response.data.statusCode || response.data.status;

                // Map Tembo status codes to our internal status - EXACT MATCH TO TEMBO DOCS
                if (temboStatus === 'PAYMENT_ACCEPTED') {
                    statusCode = 0;
                    description = "Payment accepted by customer";
                    transStatus = "SUCCESS";
                    this.saveTransactionLog(transactionId, "SUCCESS", Steps.THIRDPARTY_RESPONSE, 200, "Collection successful - customer accepted", response.data);

                } else if (temboStatus === 'PAYMENT_REJECTED') {
                    statusCode = 400;
                    description = response.data.reason || response.data.message || "Payment rejected by customer";
                    transStatus = "FAILED";
                    this.saveTransactionLog(transactionId, "FAILED", Steps.THIRDPARTY_RESPONSE, 400, description, response.data);

                } else if (temboStatus === 'PAYMENT_FAILED') {
                    statusCode = 400;
                    description = response.data.reason || response.data.message || "Payment failed - network or technical issue";
                    transStatus = "FAILED";
                    this.saveTransactionLog(transactionId, "FAILED", Steps.THIRDPARTY_RESPONSE, 400, description, response.data);

                } else if (temboStatus === 'GENERIC_ERROR') {
                    statusCode = 400;
                    description = response.data.reason || response.data.message || "Generic error occurred during payment processing";
                    transStatus = "FAILED";
                    this.saveTransactionLog(transactionId, "FAILED", Steps.THIRDPARTY_RESPONSE, 400, description, response.data);

                } else if (temboStatus === 'PENDING_ACK') {
                    statusCode = 122;
                    description = "Payment request acknowledged, waiting for customer action";
                    transStatus = "PENDING";
                    this.saveTransactionLog(transactionId, "PENDING", Steps.THIRDPARTY_RESPONSE, 202, description, response.data);

                } else {
                    // Unknown status - treat as pending but log for investigation
                    statusCode = 122;
                    description = `Unknown Tembo status: ${temboStatus}`;
                    transStatus = "PENDING";
                    this.saveTransactionLog(transactionId, "UNKNOWN", Steps.THIRDPARTY_RESPONSE, 202, description, response.data);
                }
            } else {
                // API call failed - treat as error
                statusCode = 500;
                description = response.message || "Failed to check transaction status";
                transStatus = "ONHOLD";
                this.saveTransactionLog(transactionId, "ERROR", Steps.THIRDPARTY_RESPONSE, 500, description, response);
            }

            return this.mapCollectionResponse(statusCode, description, transStatus, response.data);

        } catch (error: any) {
            console.error("❌ Error checking Tembo collection status:", error);
            const errorMessage = error?.message || "Transaction status check failed";
            this.saveTransactionLog(transactionId, "ERROR", Steps.THIRDPARTY_RESPONSE, 500, errorMessage, error);
            return this.mapCollectionResponse(500, errorMessage, "ONHOLD", error);
        }
    }

    mapCollectionResponse(statusCode: any, description: any, transStatus: any = null, data: any = null): PollingResponse {
        return {
            statusCode: statusCode,
            description: description,
            data: data,
            transStatus: transStatus
        }
    }
}

export default PollingService;