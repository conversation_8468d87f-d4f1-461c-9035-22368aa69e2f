import Model from "../helpers/model";
import Tembo from "../intergrations/Tembo";

interface DiagnosticResult {
    transactionId: string;
    currentStatus: string;
    temboStatus?: any;
    lastWebhook?: any;
    recommendations: string[];
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

interface DiagnosticSummary {
    totalPending: number;
    withReasons: number;
    withoutReasons: number;
    successRate: number;
    commonFailureReasons: { reason: string; count: number }[];
    recommendations: string[];
}

/**
 * Tembo Collections Diagnostic Service
 * Helps identify why collections are pending and provides actionable insights
 */
class TemboDiagnosticsService extends Model {
    private tembo: Tembo;

    constructor() {
        super();
        this.tembo = new Tembo();
    }

    /**
     * Run comprehensive diagnostics on Tembo collections
     */
    async runDiagnostics(hoursBack: number = 24): Promise<DiagnosticSummary> {
        console.log(`🔍 Running Tembo diagnostics for last ${hoursBack} hours...`);

        // Get pending Tembo collections
        const pendingTransactions = await this.getPendingTemboCollections(hoursBack);
        console.log(`Found ${pendingTransactions.length} pending Tembo collections`);

        const diagnosticResults: DiagnosticResult[] = [];
        
        for (const transaction of pendingTransactions) {
            const result = await this.diagnoseSingleTransaction(transaction);
            diagnosticResults.push(result);
        }

        return this.generateSummary(diagnosticResults);
    }

    /**
     * Get pending Tembo collection transactions
     */
    private async getPendingTemboCollections(hoursBack: number): Promise<any[]> {
        const query = `
            SELECT t.*, 
                   tl.description as last_log_description,
                   tl.created_at as last_log_time
            FROM transactions t
            LEFT JOIN transactions_log tl ON t.trans_id = tl.trans_id 
                AND tl.created_at = (
                    SELECT MAX(created_at) 
                    FROM transactions_log 
                    WHERE trans_id = t.trans_id
                )
            WHERE t.service_name = 'TZS_COLLECTIONS'
              AND t.trans_type = 'PULL'
              AND t.status IN ('INITIATED', 'PENDING')
              AND t.created_at >= DATE_SUB(NOW(), INTERVAL ? HOUR)
            ORDER BY t.created_at DESC
        `;

        return await this.callQuerySafe(query, [hoursBack]);
    }

    /**
     * Diagnose a single transaction
     */
    private async diagnoseSingleTransaction(transaction: any): Promise<DiagnosticResult> {
        const { trans_id, status, created_at, reason } = transaction;
        const recommendations: string[] = [];
        let severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' = 'LOW';

        // Check how long it's been pending
        const hoursOld = this.getHoursOld(created_at);
        
        if (hoursOld > 24) {
            severity = 'CRITICAL';
            recommendations.push('Transaction over 24 hours old - investigate immediately');
        } else if (hoursOld > 2) {
            severity = 'HIGH';
            recommendations.push('Transaction over 2 hours old - check Tembo status');
        } else if (hoursOld > 0.5) {
            severity = 'MEDIUM';
            recommendations.push('Transaction over 30 minutes old - monitor closely');
        }

        // Check if we have a reason
        if (!reason || reason === 'N/A' || reason.trim() === '') {
            severity = Math.max(severity === 'LOW' ? 1 : severity === 'MEDIUM' ? 2 : severity === 'HIGH' ? 3 : 4, 2) === 2 ? 'MEDIUM' : severity === 'HIGH' ? 'HIGH' : 'CRITICAL';
            recommendations.push('No failure reason available - check Tembo API status');
        }

        // Try to get current status from Tembo
        let temboStatus = null;
        try {
            const statusResponse = await this.tembo.getTransactionStatus(trans_id);
            temboStatus = statusResponse.data;
            
            if (statusResponse.status !== 200) {
                recommendations.push('Unable to fetch status from Tembo API - check connectivity');
                severity = 'HIGH';
            }
        } catch (error) {
            recommendations.push('Error connecting to Tembo API - check service health');
            severity = 'HIGH';
        }

        // Check for webhook delivery
        const lastWebhook = await this.getLastWebhook(trans_id);
        if (!lastWebhook) {
            recommendations.push('No webhook received - check webhook endpoint health');
            severity = 'HIGH';
        }

        return {
            transactionId: trans_id,
            currentStatus: status,
            temboStatus,
            lastWebhook,
            recommendations,
            severity
        };
    }

    /**
     * Get the last webhook for a transaction
     */
    private async getLastWebhook(transId: string): Promise<any> {
        const query = `
            SELECT * FROM webhook_logs 
            WHERE trans_id = ? 
            ORDER BY timestamp DESC 
            LIMIT 1
        `;
        
        const result = await this.callQuerySafe(query, [transId]);
        return result.length > 0 ? result[0] : null;
    }

    /**
     * Calculate hours since transaction creation
     */
    private getHoursOld(createdAt: string): number {
        const now = new Date();
        const created = new Date(createdAt);
        return (now.getTime() - created.getTime()) / (1000 * 60 * 60);
    }

    /**
     * Generate diagnostic summary
     */
    private generateSummary(results: DiagnosticResult[]): DiagnosticSummary {
        const totalPending = results.length;
        const withReasons = results.filter(r => 
            r.lastWebhook?.description && 
            r.lastWebhook.description !== 'N/A' && 
            r.lastWebhook.description.trim() !== ''
        ).length;
        const withoutReasons = totalPending - withReasons;
        
        // Calculate success rate (transactions that have clear status)
        const successRate = totalPending > 0 ? (withReasons / totalPending) * 100 : 0;

        // Analyze common failure reasons
        const reasonCounts: { [key: string]: number } = {};
        results.forEach(r => {
            if (r.lastWebhook?.description && r.lastWebhook.description !== 'N/A') {
                const reason = r.lastWebhook.description;
                reasonCounts[reason] = (reasonCounts[reason] || 0) + 1;
            }
        });

        const commonFailureReasons = Object.entries(reasonCounts)
            .map(([reason, count]) => ({ reason, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 5);

        // Generate recommendations
        const recommendations: string[] = [];
        
        if (successRate < 70) {
            recommendations.push('🚨 Success rate below 70% - immediate action required');
        }
        
        if (withoutReasons > totalPending * 0.3) {
            recommendations.push('📋 Over 30% of transactions lack failure reasons - improve error handling');
        }

        const criticalCount = results.filter(r => r.severity === 'CRITICAL').length;
        if (criticalCount > 0) {
            recommendations.push(`⚠️ ${criticalCount} critical transactions require immediate attention`);
        }

        recommendations.push('🔄 Implement regular polling to reduce webhook dependency');
        recommendations.push('📊 Monitor Tembo API health and response times');
        recommendations.push('🔍 Set up alerts for transactions pending over 30 minutes');

        return {
            totalPending,
            withReasons,
            withoutReasons,
            successRate: Math.round(successRate * 100) / 100,
            commonFailureReasons,
            recommendations
        };
    }

    /**
     * Force status check for specific transaction
     */
    async forceStatusCheck(transactionId: string): Promise<any> {
        console.log(`🔄 Force checking status for transaction: ${transactionId}`);
        
        try {
            const statusResponse = await this.tembo.getTransactionStatus(transactionId);
            
            if (statusResponse.status === 200 && statusResponse.data) {
                // Update transaction with latest status - EXACT TEMBO STATUS CODES
                const temboStatus = statusResponse.data.statusCode || statusResponse.data.status;
                let newStatus = 'PENDING';
                let reason = 'Status checked manually';

                if (temboStatus === 'PAYMENT_ACCEPTED') {
                    newStatus = 'RECEIVED';
                    reason = 'Payment accepted by customer (manual check)';
                } else if (temboStatus === 'PAYMENT_REJECTED') {
                    newStatus = 'FAILED';
                    reason = statusResponse.data.reason || 'Payment rejected by customer (manual check)';
                } else if (temboStatus === 'PAYMENT_FAILED') {
                    newStatus = 'FAILED';
                    reason = statusResponse.data.reason || 'Payment failed - network/technical issue (manual check)';
                } else if (temboStatus === 'GENERIC_ERROR') {
                    newStatus = 'FAILED';
                    reason = statusResponse.data.reason || 'Generic error during processing (manual check)';
                } else if (temboStatus === 'PENDING_ACK') {
                    newStatus = 'PENDING';
                    reason = 'Payment request acknowledged, waiting for customer (manual check)';
                }

                await this.updateData("transactions", `trans_id = '${transactionId}'`, {
                    status: newStatus,
                    reason: reason
                });

                console.log(`✅ Updated transaction ${transactionId} to ${newStatus}: ${reason}`);
                return { success: true, status: newStatus, reason, temboData: statusResponse.data };
            } else {
                console.log(`❌ Failed to get status for ${transactionId}`);
                return { success: false, error: 'Failed to fetch status from Tembo' };
            }
        } catch (error: any) {
            console.error(`❌ Error checking status for ${transactionId}:`, error);
            return { success: false, error: error.message };
        }
    }

    /**
     * Bulk force check for all pending transactions
     */
    async bulkForceCheck(maxTransactions: number = 50): Promise<any> {
        const pendingTransactions = await this.getPendingTemboCollections(24);
        const toCheck = pendingTransactions.slice(0, maxTransactions);
        
        console.log(`🔄 Force checking ${toCheck.length} pending transactions...`);
        
        const results = [];
        for (const transaction of toCheck) {
            const result = await this.forceStatusCheck(transaction.trans_id);
            results.push({ transactionId: transaction.trans_id, ...result });
            
            // Add small delay to avoid overwhelming Tembo API
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
        return results;
    }
}

export default TemboDiagnosticsService;
